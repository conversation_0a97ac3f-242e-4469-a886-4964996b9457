import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from matplotlib.backends.backend_pdf import PdfPages
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better looking plots
plt.style.use('default')
sns.set_palette("husl")

def load_and_preprocess_data(csv_file_path):
    """
    Load and preprocess the SCADA vs API comparison data
    Updated to match your CSV structure: Date & Time, Inverter, API_Value, SCADA_Value, Difference, Percent_Difference, Match_Status
    """
    # Read CSV file
    df = pd.read_csv(csv_file_path)
    
    # Clean column names (remove any extra spaces)
    df.columns = df.columns.str.strip()
    
    # Convert Date & Time column to datetime
    # Try to parse with automatic format detection first, then fallback to specific formats
    try:
        df['DateTime'] = pd.to_datetime(df['Date & Time'], format='ISO8601')
    except:
        try:
            df['DateTime'] = pd.to_datetime(df['Date & Time'], format='%d-%m-%Y %H:%M')
        except:
            # Let pandas infer the format automatically
            df['DateTime'] = pd.to_datetime(df['Date & Time'], infer_datetime_format=True)
    
    # Extract date for daily analysis
    df['Date'] = df['DateTime'].dt.date
    
    # Handle missing data cases - convert to numeric where possible
    # Convert API_Value and SCADA_Value to numeric, handling 'Missing Data' cases
    df['API_Value_Numeric'] = pd.to_numeric(df['API_Value'], errors='coerce')
    df['SCADA_Value_Numeric'] = pd.to_numeric(df['SCADA_Value'], errors='coerce')
    
    # Create actual difference and percentage difference for non-missing data
    mask_valid = (df['Match_Status'] != 'Missing Data') & (df['Match_Status'] != 'Missing Data')
    
    # Calculate differences for valid data points
    df.loc[mask_valid, 'Calc_Difference'] = df.loc[mask_valid, 'SCADA_Value_Numeric'] - df.loc[mask_valid, 'API_Value_Numeric']
    
    # Calculate percentage difference (avoiding division by zero)
    df.loc[mask_valid, 'Calc_Pct_Difference'] = np.where(
        df.loc[mask_valid, 'API_Value_Numeric'] != 0,
        (df.loc[mask_valid, 'Calc_Difference'] / df.loc[mask_valid, 'API_Value_Numeric']) * 100,
        0
    )
    
    # Use calculated values or original values from CSV
    df['Final_Difference'] = df['Calc_Difference'].fillna(df['Difference'] if 'Difference' in df.columns else 0)
    df['Final_Pct_Difference'] = df['Calc_Pct_Difference'].fillna(df['Percent_Difference'] if 'Percent_Difference' in df.columns else 0)
    
    # Create bins for absolute differences (focus on actual values, not percentages)
    valid_diff_data = df[df['Match_Status'] == 'Mismatch']['Final_Difference'].dropna()
    if len(valid_diff_data) > 0:
        diff_percentiles = np.percentile(valid_diff_data.abs(), [0, 25, 50, 75, 90, 95, 99, 100])
        df['Abs_Diff_Bin'] = pd.cut(np.abs(df['Final_Difference']), 
                                    bins=np.unique(diff_percentiles),
                                    labels=[f'{diff_percentiles[i]:.1f}-{diff_percentiles[i+1]:.1f}' 
                                           for i in range(len(np.unique(diff_percentiles))-1)],
                                    include_lowest=True)
    
    # Create bins for signed differences (shows direction)
    if len(valid_diff_data) > 0:
        signed_percentiles = np.percentile(valid_diff_data, [0, 10, 25, 45, 55, 75, 90, 100])
        # Ensure we have both negative and positive values
        neg_vals = signed_percentiles[signed_percentiles < 0]
        pos_vals = signed_percentiles[signed_percentiles > 0]
        
        if len(neg_vals) > 0 and len(pos_vals) > 0:
            custom_bins = np.concatenate([neg_vals, [0], pos_vals])
            custom_bins = np.unique(custom_bins)
            df['Signed_Diff_Bin'] = pd.cut(df['Final_Difference'], 
                                          bins=custom_bins,
                                          include_lowest=True)
    
    # Create bins for percentage differences
    valid_pct_data = df[df['Match_Status'] == 'Mismatch']['Final_Pct_Difference'].dropna()
    if len(valid_pct_data) > 0:
        pct_bins = [0, 5, 10, 15, 25, 50, 100, valid_pct_data.max()]
        df['Pct_Diff_Bin'] = pd.cut(np.abs(df['Final_Pct_Difference']), 
                                    bins=pct_bins,
                                    labels=['0-5%', '5-10%', '10-15%', '15-25%', '25-50%', '50-100%', '>100%'],
                                    include_lowest=True)
    
    return df

def create_visualizations(df, output_pdf_path):
    """
    Create comprehensive visualizations and save to PDF
    """
    
    with PdfPages(output_pdf_path) as pdf:
        
        # Page 1: Overview Dashboard
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('SCADA vs API Comparison - Daily Granularity Overview Dashboard', fontsize=16, fontweight='bold')
        
        # Match Status Distribution (Pie Chart)
        match_counts = df['Match_Status'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        wedges, texts, autotexts = ax1.pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        ax1.set_title('Match Status Distribution')
        
        # Match Rate by Inverter (Bar Chart)
        inverter_stats = df.groupby('Inverter').agg({
            'Match_Status': lambda x: (x == 'Match').sum() / len(x) * 100,
            'Final_Difference': lambda x: np.abs(x[x.notna()]).mean() if len(x[x.notna()]) > 0 else 0,
            'Final_Pct_Difference': lambda x: np.abs(x[x.notna()]).mean() if len(x[x.notna()]) > 0 else 0
        }).round(2)
        inverter_stats.columns = ['Match_Rate', 'Avg_Abs_Diff', 'Avg_Abs_Pct_Diff']
        
        bars = ax2.bar(range(len(inverter_stats)), inverter_stats['Match_Rate'], color='skyblue')
        ax2.set_title('Match Rate by Inverter (%)')
        ax2.set_xlabel('Inverter')
        ax2.set_ylabel('Match Rate (%)')
        ax2.set_xticks(range(len(inverter_stats)))
        ax2.set_xticklabels(inverter_stats.index, rotation=45)
        
        # Add average absolute difference as text on bars
        for i, (inv, row) in enumerate(inverter_stats.iterrows()):
            ax2.text(i, row['Match_Rate'] + 1, f'Avg Diff:\n{row["Avg_Abs_Diff"]:.2f}\n({row["Avg_Abs_Pct_Diff"]:.1f}%)', 
                    ha='center', va='bottom', fontsize=8)
        
        # Daily Analysis
        daily_stats = df.groupby('Date').agg({
            'Match_Status': lambda x: (x == 'Match').sum() / len(x) * 100,
            'Final_Difference': lambda x: np.abs(x[x.notna()]).mean() if len(x[x.notna()]) > 0 else 0,
            'Final_Pct_Difference': lambda x: np.abs(x[x.notna()]).mean() if len(x[x.notna()]) > 0 else 0
        }).round(2)
        daily_stats.columns = ['Match_Rate', 'Avg_Abs_Diff', 'Avg_Abs_Pct_Diff']

        # Convert dates to strings for plotting
        date_labels = [str(date) for date in daily_stats.index]
        x_positions = range(len(daily_stats))

        ax3.plot(x_positions, daily_stats['Avg_Abs_Diff'], marker='o', linewidth=2, color='red', label='Avg Abs Diff')
        ax3_twin = ax3.twinx()
        ax3_twin.plot(x_positions, daily_stats['Match_Rate'], marker='s', linewidth=2, color='blue', alpha=0.7, label='Match Rate %')
        ax3.set_title('Average Absolute Difference & Match Rate by Date')
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Average Absolute Difference', color='red')
        ax3_twin.set_ylabel('Match Rate (%)', color='blue')
        ax3.set_xticks(x_positions)
        ax3.set_xticklabels(date_labels, rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')
        
        # Value Distribution for Mismatches
        mismatch_data = df[df['Match_Status'] == 'Mismatch']
        if len(mismatch_data) > 0:
            valid_api = mismatch_data['API_Value_Numeric'].dropna()
            valid_scada = mismatch_data['SCADA_Value_Numeric'].dropna()
            
            if len(valid_api) > 0 and len(valid_scada) > 0:
                ax4.scatter(valid_api, valid_scada, alpha=0.5, s=20, c='red')
                
                # Add perfect correlation line
                max_val = max(valid_api.max(), valid_scada.max())
                min_val = min(valid_api.min(), valid_scada.min())
                ax4.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2, label='Perfect Match')
                
                ax4.set_xlabel('API Value')
                ax4.set_ylabel('SCADA Value')
                ax4.set_title('SCADA vs API Values (Mismatches Only)')
                ax4.legend()
                ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 2: Difference Analysis
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Detailed Difference Analysis', fontsize=16, fontweight='bold')
        
        # Absolute Difference Distribution
        if 'Abs_Diff_Bin' in df.columns:
            abs_diff_counts = df['Abs_Diff_Bin'].value_counts().sort_index()
            if len(abs_diff_counts) > 0:
                bars = ax1.bar(range(len(abs_diff_counts)), abs_diff_counts.values, 
                              color='lightcoral', alpha=0.7)
                ax1.set_title('Distribution of Absolute Differences')
                ax1.set_xlabel('Absolute Difference Bins')
                ax1.set_ylabel('Count')
                ax1.set_xticks(range(len(abs_diff_counts)))
                ax1.set_xticklabels(abs_diff_counts.index, rotation=45, ha='right')
                
                # Add count labels on bars
                for bar, count in zip(bars, abs_diff_counts.values):
                    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01, 
                            str(count), ha='center', va='bottom', fontsize=9)
        
        # Signed Difference Distribution
        if 'Signed_Diff_Bin' in df.columns:
            signed_diff_counts = df['Signed_Diff_Bin'].value_counts().sort_index()
            if len(signed_diff_counts) > 0:
                bars = ax2.bar(range(len(signed_diff_counts)), signed_diff_counts.values, 
                              color='lightblue', alpha=0.7)
                ax2.set_title('Signed Difference Distribution (SCADA - API)')
                ax2.set_xlabel('Signed Difference Bins')
                ax2.set_ylabel('Count')
                ax2.set_xticks(range(len(signed_diff_counts)))
                ax2.set_xticklabels([str(x) for x in signed_diff_counts.index], rotation=45, ha='right')
                
                # Add percentage labels on bars
                total_count = signed_diff_counts.sum()
                for bar, count in zip(bars, signed_diff_counts.values):
                    percentage = (count / total_count) * 100
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01, 
                            f'{count}\n({percentage:.1f}%)', ha='center', va='bottom', fontsize=8)
        
        # Percentage Difference Distribution
        if 'Pct_Diff_Bin' in df.columns:
            pct_diff_counts = df['Pct_Diff_Bin'].value_counts().sort_index()
            if len(pct_diff_counts) > 0:
                bars = ax3.bar(range(len(pct_diff_counts)), pct_diff_counts.values, 
                              color='lightgreen', alpha=0.7)
                ax3.set_title('Distribution of Percentage Differences')
                ax3.set_xlabel('Percentage Difference Bins')
                ax3.set_ylabel('Count')
                ax3.set_xticks(range(len(pct_diff_counts)))
                ax3.set_xticklabels(pct_diff_counts.index, rotation=45)
                
                # Add count labels on bars
                for bar, count in zip(bars, pct_diff_counts.values):
                    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01, 
                            str(count), ha='center', va='bottom', fontsize=9)
        
        # System Bias Analysis
        mismatch_data = df[df['Match_Status'] == 'Mismatch']
        if len(mismatch_data) > 0:
            scada_higher = mismatch_data[mismatch_data['Final_Difference'] > 0]
            api_higher = mismatch_data[mismatch_data['Final_Difference'] < 0]
            
            scada_higher_count = len(scada_higher)
            api_higher_count = len(api_higher)
            
            categories = ['SCADA Higher', 'API Higher']
            counts = [scada_higher_count, api_higher_count]
            colors = ['#ff6b6b', '#4ecdc4']
            
            bars = ax4.bar(categories, counts, color=colors, alpha=0.8)
            ax4.set_title('System Bias Analysis\n(Which System Reports Higher Values)')
            ax4.set_ylabel('Count of Mismatches')
            
            # Add percentage labels
            total_mismatches = scada_higher_count + api_higher_count
            for bar, count in zip(bars, counts):
                percentage = (count / total_mismatches) * 100 if total_mismatches > 0 else 0
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.02, 
                        f'{count}\n({percentage:.1f}%)', 
                        ha='center', va='bottom', fontsize=12,
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 3: Inverter Performance Analysis
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('Individual Inverter Performance Analysis', fontsize=16, fontweight='bold')
        
        inverters = sorted(df['Inverter'].unique())
        for i, inverter in enumerate(inverters):
            if i < 9:  # Handle up to 9 inverters
                row, col = i // 3, i % 3
                ax = axes[row, col]
                
                inv_data = df[df['Inverter'] == inverter]
                match_counts = inv_data['Match_Status'].value_counts()
                
                # Create pie chart
                wedges, texts, autotexts = ax.pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%',
                       startangle=90, colors=['#ff9999', '#66b3ff', '#99ff99'])
                ax.set_title(f'{inverter}\nTotal: {len(inv_data)} records')
                
                # Add statistics
                match_rate = (inv_data['Match_Status'] == 'Match').sum() / len(inv_data) * 100
                mismatch_data = inv_data[inv_data['Match_Status'] == 'Mismatch']
                avg_pct_diff = mismatch_data['Final_Pct_Difference'].abs().mean() if len(mismatch_data) > 0 else 0
                
                ax.text(0, -1.3, f'Match Rate: {match_rate:.1f}%\nAvg Pct Diff: {avg_pct_diff:.1f}%', 
                       ha='center', va='center', fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.7))
        
        # Hide empty subplots
        for i in range(len(inverters), 9):
            row, col = i // 3, i % 3
            axes[row, col].axis('off')
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 4: Summary Statistics
        fig, ax = plt.subplots(figsize=(16, 10))
        ax.axis('tight')
        ax.axis('off')
        
        # Create summary statistics table
        summary_stats = []
        
        # Overall statistics
        total_records = len(df)
        match_count = (df['Match_Status'] == 'Match').sum()
        mismatch_count = (df['Match_Status'] == 'Mismatch').sum()
        missing_count = (df['Match_Status'] == 'Missing Data').sum()
        match_rate = match_count / total_records * 100
        
        summary_stats.append(['OVERALL STATISTICS', '', ''])
        summary_stats.append(['Total Records', f'{total_records:,}', ''])
        summary_stats.append(['Matches', f'{match_count:,} ({match_rate:.1f}%)', ''])
        summary_stats.append(['Mismatches', f'{mismatch_count:,} ({mismatch_count/total_records*100:.1f}%)', ''])
        summary_stats.append(['Missing Data', f'{missing_count:,} ({missing_count/total_records*100:.1f}%)', ''])
        summary_stats.append(['', '', ''])
        
        # Date range
        if 'DateTime' in df.columns:
            date_range = f"{df['DateTime'].min().strftime('%d-%m-%Y')} to {df['DateTime'].max().strftime('%d-%m-%Y')}"
            summary_stats.append(['Date Range', date_range, ''])
            summary_stats.append(['', '', ''])
        
        # Per inverter statistics
        summary_stats.append(['INVERTER PERFORMANCE', 'Match Rate', 'Avg Pct Diff'])
        for inverter in sorted(df['Inverter'].unique()):
            inv_data = df[df['Inverter'] == inverter]
            inv_match_rate = (inv_data['Match_Status'] == 'Match').sum() / len(inv_data) * 100
            inv_mismatch_data = inv_data[inv_data['Match_Status'] == 'Mismatch']
            inv_avg_pct_diff = inv_mismatch_data['Final_Pct_Difference'].abs().mean() if len(inv_mismatch_data) > 0 else 0
            summary_stats.append([inverter, f'{inv_match_rate:.1f}%', f'{inv_avg_pct_diff:.1f}%'])
        
        # Create table
        table = ax.table(cellText=summary_stats, 
                        colLabels=['Metric', 'Value', 'Additional'],
                        cellLoc='center',
                        loc='center',
                        colWidths=[0.4, 0.3, 0.3])
        
        table.auto_set_font_size(False)
        table.set_fontsize(11)
        table.scale(1.2, 2)
        
        # Style the table
        for i in range(len(summary_stats) + 1):
            for j in range(3):
                cell = table[(i, j)]
                if i == 0:  # Header
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                elif i > 0 and summary_stats[i-1][0] in ['OVERALL STATISTICS', 'INVERTER PERFORMANCE']:
                    cell.set_facecolor('#E8F5E8')
                    cell.set_text_props(weight='bold')
        
        ax.set_title('SCADA vs API Analysis - Daily Granularity Summary Report', fontsize=16, fontweight='bold', pad=20)
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()

def main():
    """
    Main function to run the analysis
    """
    # File paths - UPDATE THESE PATHS FOR DAILY GRANULARITY
    csv_file_path = 'validation_results/comparison_results.csv'  # Replace with your CSV file path
    output_pdf_path = 'Analysis_Report_Daily.pdf'
    
    try:
        # Load and preprocess data
        print("Loading and preprocessing data...")
        df = load_and_preprocess_data(csv_file_path)
        print(f"Data loaded successfully. Shape: {df.shape}")
        
        # Print basic statistics
        print("\nBasic Statistics:")
        print(f"Total records: {len(df):,}")
        print(f"Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
        print(f"Match rate: {(df['Match_Status'] == 'Match').sum() / len(df) * 100:.1f}%")
        print(f"Unique inverters: {df['Inverter'].nunique()}")
        
        # Print match status distribution
        print("\nMatch Status Distribution:")
        print(df['Match_Status'].value_counts())
        
        # Create visualizations
        print("\nCreating visualizations...")
        create_visualizations(df, output_pdf_path)
        print(f"Analysis complete! Report saved as: {output_pdf_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Please check your CSV file path and format.")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# Additional utility functions for custom analysis

def analyze_specific_inverter(df, inverter_id):
    """
    Detailed analysis for a specific inverter
    """
    inv_data = df[df['Inverter'] == inverter_id]
    
    print(f"\nDetailed Analysis for {inverter_id}:")
    print(f"Total records: {len(inv_data)}")
    print(f"Match rate: {(inv_data['Match_Status'] == 'Match').sum() / len(inv_data) * 100:.1f}%")
    
    mismatch_data = inv_data[inv_data['Match_Status'] == 'Mismatch']
    if len(mismatch_data) > 0:
        print(f"Average percentage difference: {mismatch_data['Final_Pct_Difference'].abs().mean():.2f}%")
        print(f"Max percentage difference: {mismatch_data['Final_Pct_Difference'].abs().max():.2f}%")
    
    return inv_data

def analyze_date_period(df, start_date, end_date):
    """
    Analyze data for a specific date period
    """
    # Convert string dates to date objects if needed
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date).date()
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date).date()

    period_data = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]

    print(f"\nAnalysis for dates {start_date} to {end_date}:")
    print(f"Total records: {len(period_data)}")
    print(f"Match rate: {(period_data['Match_Status'] == 'Match').sum() / len(period_data) * 100:.1f}%")

    return period_data

def print_detailed_analysis(df):
    """
    Print detailed analysis similar to your report
    """
    print("\nDETAILED ANALYSIS RESULTS")
    print("=" * 50)
    
    total_records = len(df)
    match_rate = (df['Match_Status'] == 'Match').sum() / total_records * 100
    
    print(f"Total Comparisons: {total_records:,}")
    print(f"Match Rate: {match_rate:.1f}%")
    print("Match Status Distribution:")
    print(df['Match_Status'].value_counts())
    
    print("\nAnalysis by Inverter:")
    inverter_analysis = df.groupby('Inverter').agg({
        'Match_Status': [
            lambda x: (x == 'Match').sum(),  # Matches
            'count'  # Total
        ],
        'Final_Pct_Difference': [
            lambda x: x[x.notna()].abs().mean() if len(x[x.notna()]) > 0 else 0,  # Avg_Pct_Diff
            lambda x: x[x.notna()].abs().max() if len(x[x.notna()]) > 0 else 0,   # Max_Pct_Diff
            lambda x: x[x.notna()].abs().std() if len(x[x.notna()]) > 0 else 0    # Std_Pct_Diff
        ],
        'Final_Difference': [
            lambda x: x[x.notna()].abs().mean() if len(x[x.notna()]) > 0 else 0,  # Avg_Diff
            lambda x: x[x.notna()].abs().max() if len(x[x.notna()]) > 0 else 0    # Max_Diff
        ]
    }).round(2)
    
    # Flatten column names
    inverter_analysis.columns = ['Matches', 'Total', 'Avg_Pct_Diff', 'Max_Pct_Diff', 'Std_Pct_Diff', 'Avg_Diff', 'Max_Diff']
    inverter_analysis['Match_Rate'] = (inverter_analysis['Matches'] / inverter_analysis['Total'] * 100).round(1)
    
    print(inverter_analysis[['Matches', 'Avg_Pct_Diff', 'Max_Pct_Diff', 'Std_Pct_Diff', 'Avg_Diff', 'Max_Diff', 'Total', 'Match_Rate']])
    
    print("\nAnalysis by Date:")
    daily_analysis = df.groupby('Date').agg({
        'Match_Status': [
            lambda x: (x == 'Match').sum(),  # Match_Status (count of matches)
            'count'  # Total
        ],
        'Final_Pct_Difference': lambda x: x[x.notna()].abs().mean() if len(x[x.notna()]) > 0 else 0
    }).round(2)

    daily_analysis.columns = ['Match_Status', 'Total', 'Percent_Difference']
    daily_analysis['Match_Rate'] = (daily_analysis['Match_Status'] / daily_analysis['Total'] * 100).round(1)

    print(daily_analysis[['Match_Status', 'Percent_Difference', 'Total', 'Match_Rate']])
    
    print("\nMismatch Patterns:")
    mismatch_data = df[df['Match_Status'] == 'Mismatch']
    if len(mismatch_data) > 0:
        api_higher = len(mismatch_data[mismatch_data['Final_Difference'] < 0])
        scada_higher = len(mismatch_data[mismatch_data['Final_Difference'] > 0])
        total_mismatches = api_higher + scada_higher
        
        if total_mismatches > 0:
            api_pct = (api_higher / total_mismatches) * 100
            scada_pct = (scada_higher / total_mismatches) * 100
            print(f"API Higher: {api_higher} ({api_pct:.1f}%)")
            print(f"SCADA Higher: {scada_higher} ({scada_pct:.1f}%)")